<template>
  <div class="topbar">
    <img src="../assets/logo.svg" alt="logo" />
    <div class="nav">
      <h4>首页</h4>
      <h4>关于我们</h4>
      <h4>应用服务</h4>
      <h4>服务体验</h4>
      <h4>订阅</h4>
    </div>
    <div class="topbutton">
      <el-button>登录</el-button>
      <el-button>注册</el-button>
    </div>
  </div>
  <div>
    

  </div>
</template>

<script setup lang="ts">
import { ElButton } from 'element-plus';
import { onMounted } from 'vue'

onMounted(() => {
  
})
</script>

<style scoped>
.topbar {
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
}

.nav {
  display: flex;
  gap: 50px; /* 可选：设置子元素间距 */
}

.topbutton {
  padding: 0 24px;
}

</style>