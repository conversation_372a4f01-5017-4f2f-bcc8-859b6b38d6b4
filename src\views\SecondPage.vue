<template>
  <div class="second-page-container">
    <div class="second-content">
      <h1>第二页内容</h1>
      <p>这里是第二页的内容区域</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// 第二页组件逻辑
</script>
<style scoped>
.second-page-container {
    background: url("../assets/second_background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    min-height: 100vh;
    margin: 0 24px;
    border-radius: 0 0 20px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.second-content {
    text-align: center;
    color: white;
    padding: 40px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .second-page-container {
    margin: 0 10px;
    border-radius: 0 0 15px 15px;
  }

  .second-content {
    padding: 20px;
  }
}
</style>