import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import IndexView from './views/index.vue'

// 创建路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: IndexView
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建应用实例并使用路由
const app = createApp(App)
app.use(router)
app.mount('#app')
