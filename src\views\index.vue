<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <div class="topbar">
      <img src="../assets/logo.svg" alt="logo" class="logo" />
      <div class="nav">
        <span class="nav-item active">首页</span>
        <span class="nav-item">关于我们</span>
        <span class="nav-item">应用服务</span>
        <span class="nav-item">服务体验</span>
        <span class="nav-item">订阅</span>
      </div>
      <div class="topbutton">
        <el-button class="login-btn">登录</el-button>
        <el-button type="primary" class="register-btn">注册</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="center-content">
        <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />

        <el-button type="primary" size="large" class="experience-btn">
          立即体验
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElButton } from 'element-plus';
import { onMounted } from 'vue'

onMounted(() => {
  
})
</script>

<style scoped>
.home-container {
  min-height: 80vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  margin: 20px;
  border-radius: 20px;
  overflow: hidden;
}

.topbar {
  padding: 8px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
  height: 60px;
}

.logo {
  height: 32px;
  width: auto;
}

.nav {
  display: flex;
  gap: 40px;
  align-items: center;
}

.nav-item {
  color: break;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 8px 0;
}

.nav-item:hover {
  color: #5B9BD5;
}

.nav-item.active {
  color: #5B9BD5;
  font-weight: 500;
}

.topbutton {
  display: flex;
  gap: 12px;
  align-items: center;
}

.login-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #5B9BD5;
}

.register-btn {
  background: #5B9BD5;
  border: none;
}

.main-content {
  background-image: url('../assets/banner.png');
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(80vh - 60px);
  padding: 0 40px;
}

.center-content {
  text-align: center;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.main-logo {
  max-width: 600px;
  width: 100%;
  height: auto;
  margin-bottom: 30px;
}

.subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  letter-spacing: 2px;
}

.experience-btn {
  font-size: 16px;
  padding: 12px 60px;
  background: #5B9BD5;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  min-width: 140px;
}

.experience-btn:hover {
  background: #7BB3E0;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(91, 155, 213, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    margin: 10px;
    border-radius: 15px;
  }

  .topbar {
    padding: 8px 20px;
    flex-direction: column;
    gap: 10px;
    height: auto;
    min-height: 60px;
  }

  .nav {
    gap: 20px;
  }

  .nav-item {
    font-size: 14px;
  }

  .main-content {
    padding: 0 20px;
    min-height: calc(80vh - 80px);
  }

  .main-logo {
    max-width: 400px;
  }

  .experience-btn {
    padding: 10px 50px;
    font-size: 14px;
  }
}
</style>